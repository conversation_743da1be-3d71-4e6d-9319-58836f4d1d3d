<script setup lang="ts">
import { ref, computed } from 'vue';
import { useLayout } from '@/layout/composables/layout';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';
import { validateEmail, validateRequired } from '@/utils';
import { requestCreateTenant } from '@/shared';
import FloatingConfigurator from '@/components/FloatingConfigurator.vue';
import { VueTelInput } from 'vue-tel-input';
import 'vue-tel-input/vue-tel-input.css';

const authStore = useAuthStore();

const { logo } = useLayout();
const toast = useToast();

const name = ref('');
const nameError = ref('');

const companyName = ref('');
const companyNameError = ref('');

const email = ref('');
const emailError = ref('');

const password = ref('');
const passwordError = ref('');

const confirmPassword = ref('');
const confirmPasswordError = ref('');

const phone = ref('');
const phoneError = ref('');
const isPhoneValid = ref(false);

const checked = ref(false);
const loading = ref(false);
const tenantCreateDialog = ref(false);

const validateCompanyName = () => {
    companyNameError.value = validateRequired(companyName.value);
};

const validateFullName = () => {
    nameError.value = validateRequired(name.value);
};

const validateEmailField = () => {
    emailError.value = validateRequired(email.value) || validateEmail(email.value);
};

const validatePasswordField = () => {
    passwordError.value = password.value.length < 8 ? 'Password must be at least 8 characters' : '';
};
const validatePassword = () => {
    passwordError.value = validateRequired(password.value);
    if (confirmPassword.value) {
        validateConfirmPassword();
    }
};

const validateConfirmPassword = () => {
    if (password.value !== confirmPassword.value) {
        confirmPasswordError.value = 'Passwords do not match.';
    } else {
        confirmPasswordError.value = '';
    }
};

const onPhoneInput = (newPhone: string, phoneObject: { valid: boolean }) => {
    isPhoneValid.value = phoneObject.valid;
    if (phone.value && !isPhoneValid.value) {
        phoneError.value = 'Please enter a valid phone number.';
    } else {
        phoneError.value = '';
    }
};

const validateForm = computed(() => {
    return !!nameError.value || !!companyNameError.value || !!emailError.value || !!passwordError.value || !!confirmPasswordError.value || !isPhoneValid.value || !checked.value;
});

const validateGoogleForm = computed(() => {
    return companyName.value && isPhoneValid.value;
});

const handleRegister = async () => {
    try {
        loading.value = true;

        const tenantId = await requestCreateTenant({
            userFullName: name.value,
            name: companyName.value,
            emailAddress: email.value,
            userPassword: password.value,
            phone: phone.value,
            address: '', // Default value
            status: 'trial',
            subscriptionPlan: 'free',
            approved: false,
            createdAt: new Date(),
            updatedAt: new Date()
        });

        if (tenantId) {
            tenantCreateDialog.value = true;
        }
    } catch (error: any) {
        toast.add({ severity: 'error', summary: 'Registration Failed', detail: error.message, life: 3000 });
    } finally {
        loading.value = false;
    }
};

const handleGoogleSignUp = async () => {
    try {
        await authStore.loginWithGoogle();
        if (authStore.user?.uid) {
            const tenantId = await requestCreateTenant({
                userFullName: authStore.user.displayName || '',
                name: companyName.value,
                emailAddress: authStore.user.email || '',
                phone: phone.value,
                address: '', // Default value
                status: 'trial',
                subscriptionPlan: 'free',
                approved: false,
                createdAt: new Date(),
                updatedAt: new Date()
            });
            if (tenantId) {
                tenantCreateDialog.value = true;
            }
        }
    } catch (error: any) {
        toast.add({ severity: 'error', summary: 'Google Sign-Up Failed', detail: error.message, life: 3000 });
    }
};
</script>

<template>
    <FloatingConfigurator />
    <div class="bg-surface-50 dark:bg-surface-950 flex min-h-screen">
        <div class="flex flex-col md:flex-row w-full max-w-7xl mx-auto my-8 rounded-2xl overflow-hidden shadow-2xl">
            <!-- Form Section -->
            <div class="w-full md:w-3/5 bg-surface-0 dark:bg-surface-900 p-8">
                <div class="max-w-xl mx-auto">
                    <div class="text-center mb-8">
                        <img :src="`/logo/${logo}`" class="mb-8 w-16 shrink-0 mx-auto" />
                        <h2 class="text-3xl font-bold text-surface-900 dark:text-surface-0">Create Your Account</h2>
                        <p class="text-surface-600 dark:text-surface-400 mt-2">Start your 30-day free trial. No credit card required.</p>
                    </div>

                    <div class="space-y-6">
                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Company Name</label>
                            <InputText v-model="companyName" @input="validateCompanyName" :class="{ 'p-invalid': companyNameError }" placeholder="Your Company Name" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="companyNameError">{{ companyNameError }}</small>
                        </div>

                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Full Name</label>
                            <InputText v-model="name" @input="validateFullName" :class="{ 'p-invalid': nameError }" placeholder="John Doe" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="nameError">{{ nameError }}</small>
                        </div>

                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Email</label>
                            <InputText v-model="email" @input="validateEmailField" :class="{ 'p-invalid': emailError }" placeholder="<EMAIL>" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="emailError">{{ emailError }}</small>
                        </div>

                        <div class="field">
                            {{ phone }}
                            <label for="phone" class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Phone Number</label>
                            <vue-tel-input v-model="phone" :preferredCountries="['BR', 'AU', 'US', 'CA']" :required="true" mode="international" @validate="onPhoneInput" class="w-full" fluid autofocus></vue-tel-input>
                            <small class="text-red-500" v-if="phoneError">{{ phoneError }}</small>
                        </div>
                        <div class="field">
                            <label for="password" class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Password</label>
                            <Password id="password" v-model="password" toggleMask :feedback="false" @input="validatePasswordField" @blur="validatePassword" :invalid="!!passwordError" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="passwordError">{{ passwordError }}</small>
                        </div>
                        <div class="field">
                            <label for="confirmPassword" class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Confirm Password</label>
                            <Password id="confirmPassword" v-model="confirmPassword" toggleMask :feedback="false" @blur="validateConfirmPassword" :invalid="!!confirmPasswordError" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="confirmPasswordError">{{ confirmPasswordError }}</small>
                        </div>

                        <Button label="Create Account" icon="pi pi-user-plus" class="w-full p-button-primary" :disabled="!validateForm" :loading="loading" @click="handleRegister" />

                        <Divider align="center" type="dashed">
                            <span class="text-surface-600 dark:text-surface-400">or register with</span>
                        </Divider>

                        <Button :disabled="!validateGoogleForm" icon="pi pi-google" label="Google" outlined class="w-full p-button-outlined" @click="handleGoogleSignUp" />

                        <Divider align="center" type="dashed">
                            <span class="text-surface-600 dark:text-surface-400" />
                        </Divider>

                        <Button label="Sign In" class="w-full mt-3" severity="secondary" outlined as="router-link" to="/login"></Button>
                    </div>
                </div>
            </div>

            <!-- Info Section -->
            <div class="w-full md:w-2/5 bg-primary p-8 text-white">
                <div class="h-full flex flex-col justify-center">
                    <h1 class="text-4xl font-bold mb-6">Transform Your Business Today</h1>
                    <p class="text-lg mb-8 opacity-90">Join thousands of successful businesses using our platform to grow.</p>

                    <div class="space-y-4">
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>Advanced Analytics Dashboard</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>Team Collaboration Tools</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>24/7 Premium Support</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <Dialog v-model:visible="tenantCreateDialog" :style="{ width: '450px' }" header="Success!" :modal="true">
        <div class="flex items-center">
            <i class="pi pi-check-circle mr-3" style="font-size: 2rem" />
            <span>Please wait for an email invitation from Liftt — your administrator will send it once your account has been set up.</span>
        </div>
        <template #footer>
            <Button label="Close" icon="pi pi-times" text @click="tenantCreateDialog = false"></Button>
        </template>
    </Dialog>
</template>
