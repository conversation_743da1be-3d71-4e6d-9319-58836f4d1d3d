<script setup lang="ts">
import { ref, computed } from 'vue';
import { useLayout } from '@/layout/composables/layout';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';
import { validateEmail, validateRequired } from '@/utils';
import { requestCreateTenant } from '@/shared';
import FloatingConfigurator from '@/components/FloatingConfigurator.vue';
import { VueTelInput } from 'vue-tel-input';
import 'vue-tel-input/vue-tel-input.css';

const authStore = useAuthStore();

const { logo } = useLayout();
const toast = useToast();

const name = ref('');
const nameError = ref('');

const companyName = ref('');
const companyNameError = ref('');

const email = ref('');
const emailError = ref('');

const password = ref('');
const passwordError = ref('');

const confirmPassword = ref('');
const confirmPasswordError = ref('');

const phone = ref('');
const phoneError = ref('');
const isPhoneValid = ref(false);

const checked = ref(false);
const loading = ref(false);
const tenantCreateDialog = ref(false);

const validateCompanyName = () => {
    companyNameError.value = validateRequired(companyName.value);
};

const validateFullName = () => {
    nameError.value = validateRequired(name.value);
};

const validateEmailField = () => {
    emailError.value = validateRequired(email.value) || validateEmail(email.value);
};

const validatePasswordField = () => {
    passwordError.value = password.value.length < 8 ? 'Password must be at least 8 characters' : '';
};
const validatePassword = () => {
    passwordError.value = validateRequired(password.value);
    if (confirmPassword.value) {
        validateConfirmPassword();
    }
};

const validateConfirmPassword = () => {
    if (password.value !== confirmPassword.value) {
        confirmPasswordError.value = 'Passwords do not match.';
    } else {
        confirmPasswordError.value = '';
    }
};

const onPhoneValidate = (phoneObject: { valid: boolean; country?: { iso2: string } }) => {
    isPhoneValid.value = phoneObject?.valid;

    if (phone.value && !isPhoneValid.value) {
        phoneError.value = 'Please enter a valid phone number for the selected country.';
    } else {
        phoneError.value = '';
    }
};

const validateForm = computed(() => {
    return !!nameError.value || !!companyNameError.value || !!emailError.value || !!passwordError.value || !!confirmPasswordError.value || !!phoneError.value || !phone.value || !isPhoneValid.value || !checked.value;
});

const validateGoogleForm = computed(() => {
    return companyName.value && phone.value && isPhoneValid.value && !phoneError.value;
});

const handleRegister = async () => {
    try {
        loading.value = true;

        const tenantId = await requestCreateTenant({
            userFullName: name.value,
            name: companyName.value,
            emailAddress: email.value,
            userPassword: password.value,
            phone: phone.value,
            address: '', // Default value
            status: 'trial',
            subscriptionPlan: 'free',
            approved: false,
            createdAt: new Date(),
            updatedAt: new Date()
        });

        if (tenantId) {
            tenantCreateDialog.value = true;
        }
    } catch (error: any) {
        toast.add({ severity: 'error', summary: 'Registration Failed', detail: error.message, life: 3000 });
    } finally {
        loading.value = false;
    }
};

const handleGoogleSignUp = async () => {
    try {
        await authStore.loginWithGoogle();
        if (authStore.user?.uid) {
            const tenantId = await requestCreateTenant({
                userFullName: authStore.user.displayName || '',
                name: companyName.value,
                emailAddress: authStore.user.email || '',
                phone: phone.value,
                address: '', // Default value
                status: 'trial',
                subscriptionPlan: 'free',
                approved: false,
                createdAt: new Date(),
                updatedAt: new Date()
            });
            if (tenantId) {
                tenantCreateDialog.value = true;
            }
        }
    } catch (error: any) {
        toast.add({ severity: 'error', summary: 'Google Sign-Up Failed', detail: error.message, life: 3000 });
    }
};
</script>

<template>
    <FloatingConfigurator />
    <div class="bg-surface-50 dark:bg-surface-950 flex min-h-screen">
        <div class="flex flex-col md:flex-row w-full max-w-7xl mx-auto my-8 rounded-2xl overflow-hidden shadow-2xl">
            <!-- Form Section -->
            <div class="w-full md:w-3/5 bg-surface-0 dark:bg-surface-900 p-8">
                <div class="max-w-xl mx-auto">
                    <div class="text-center mb-8">
                        <img :src="`/logo/${logo}`" class="mb-8 w-16 shrink-0 mx-auto" />
                        <h2 class="text-3xl font-bold text-surface-900 dark:text-surface-0">Create Your Account</h2>
                        <p class="text-surface-600 dark:text-surface-400 mt-2">Start your 30-day free trial. No credit card required.</p>
                    </div>

                    <div class="space-y-6">
                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Company Name</label>
                            <InputText v-model="companyName" @input="validateCompanyName" :class="{ 'p-invalid': companyNameError }" placeholder="Your Company Name" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="companyNameError">{{ companyNameError }}</small>
                        </div>

                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Full Name</label>
                            <InputText v-model="name" @input="validateFullName" :class="{ 'p-invalid': nameError }" placeholder="John Doe" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="nameError">{{ nameError }}</small>
                        </div>

                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Email</label>
                            <InputText v-model="email" @input="validateEmailField" :class="{ 'p-invalid': emailError }" placeholder="<EMAIL>" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="emailError">{{ emailError }}</small>
                        </div>

                        <div class="field">
                            <label for="phone" class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Phone Number</label>
                            <vue-tel-input
                                v-model="phone"
                                :onlyCountries="['GB', 'AU', 'US', 'CA']"
                                :required="true"
                                mode="international"
                                @validate="onPhoneValidate"
                                :inputOptions="{
                                    placeholder: 'Enter phone number',
                                    maxlength: 25,
                                    type: 'tel'
                                }"
                                :dropdownOptions="{
                                    showDialCodeInSelection: true,
                                    showFlags: true,
                                    showSearchBox: true
                                }"
                                :validCharactersOnly="true"
                                :autoDefaultCountry="false"
                                :ignoredCountries="[]"
                                :class="['vue-tel-input-custom w-full', { error: phoneError }]"
                            />
                            <small class="text-red-500" v-if="phoneError">{{ phoneError }}</small>
                        </div>
                        <div class="field">
                            <label for="password" class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Password</label>
                            <Password id="password" v-model="password" toggleMask :feedback="false" @input="validatePasswordField" @blur="validatePassword" :invalid="!!passwordError" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="passwordError">{{ passwordError }}</small>
                        </div>
                        <div class="field">
                            <label for="confirmPassword" class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Confirm Password</label>
                            <Password id="confirmPassword" v-model="confirmPassword" toggleMask :feedback="false" @blur="validateConfirmPassword" :invalid="!!confirmPasswordError" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="confirmPasswordError">{{ confirmPasswordError }}</small>
                        </div>

                        <div class="field">
                            <div class="flex items-center">
                                <Checkbox v-model="checked" inputId="terms" binary />
                                <label for="terms" class="ml-2 text-surface-900 dark:text-surface-0"> I agree to the <a href="#" class="text-primary hover:underline">Terms and Conditions</a> </label>
                            </div>
                        </div>

                        <Button label="Create Account" icon="pi pi-user-plus" class="w-full p-button-primary" :disabled="validateForm" :loading="loading" @click="handleRegister" />

                        <Divider align="center" type="dashed">
                            <span class="text-surface-600 dark:text-surface-400">or register with</span>
                        </Divider>

                        <Button :disabled="!validateGoogleForm" icon="pi pi-google" label="Google" outlined class="w-full p-button-outlined" @click="handleGoogleSignUp" />

                        <Divider align="center" type="dashed">
                            <span class="text-surface-600 dark:text-surface-400" />
                        </Divider>

                        <Button label="Sign In" class="w-full mt-3" severity="secondary" outlined as="router-link" to="/login"></Button>
                    </div>
                </div>
            </div>

            <!-- Info Section -->
            <div class="w-full md:w-2/5 bg-primary p-8 text-white">
                <div class="h-full flex flex-col justify-center">
                    <h1 class="text-4xl font-bold mb-6">Transform Your Business Today</h1>
                    <p class="text-lg mb-8 opacity-90">Join thousands of successful businesses using our platform to grow.</p>

                    <div class="space-y-4">
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>Advanced Analytics Dashboard</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>Team Collaboration Tools</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>24/7 Premium Support</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <Dialog v-model:visible="tenantCreateDialog" :style="{ width: '450px' }" header="Success!" :modal="true">
        <div class="flex items-center">
            <i class="pi pi-check-circle mr-3" style="font-size: 2rem" />
            <span>Please wait for an email invitation from Liftt — your administrator will send it once your account has been set up.</span>
        </div>
        <template #footer>
            <Button label="Close" icon="pi pi-times" text @click="tenantCreateDialog = false"></Button>
        </template>
    </Dialog>
</template>

<style scoped>
/* Vue Tel Input Custom Styling for Light and Dark Mode */
:deep(.vue-tel-input-custom) {
    width: 100%;
}

:deep(.vue-tel-input-custom .vti__dropdown) {
    background-color: var(--p-inputtext-background);
    border: 1px solid var(--p-inputtext-border-color);
    border-radius: var(--p-inputtext-border-radius);
    color: var(--p-inputtext-color);
}

:deep(.vue-tel-input-custom .vti__dropdown:hover) {
    border-color: var(--p-inputtext-hover-border-color);
}

:deep(.vue-tel-input-custom .vti__dropdown:focus-within) {
    border-color: var(--p-inputtext-focus-border-color);
    box-shadow: var(--p-inputtext-focus-ring-shadow);
    outline: var(--p-inputtext-focus-ring-width) var(--p-inputtext-focus-ring-style) var(--p-inputtext-focus-ring-color);
    outline-offset: var(--p-inputtext-focus-ring-offset);
}

:deep(.vue-tel-input-custom .vti__input) {
    background-color: transparent;
    border: none;
    color: var(--p-inputtext-color);
    font-size: var(--p-inputtext-font-size);
    padding: var(--p-inputtext-padding-y) var(--p-inputtext-padding-x);
    outline: none;
    width: 100%;
}

:deep(.vue-tel-input-custom .vti__input::placeholder) {
    color: var(--p-inputtext-placeholder-color);
}

:deep(.vue-tel-input-custom .vti__selection) {
    background-color: transparent;
    border-right: 1px solid var(--p-inputtext-border-color);
    color: var(--p-inputtext-color);
    padding: var(--p-inputtext-padding-y) 8px;
}

:deep(.vue-tel-input-custom .vti__flag) {
    margin-right: 8px;
}

:deep(.vue-tel-input-custom .vti__dropdown-list) {
    background-color: var(--p-overlay-popover-background);
    border: 1px solid var(--p-overlay-popover-border-color);
    border-radius: var(--p-overlay-popover-border-radius);
    box-shadow: var(--p-overlay-popover-shadow);
    color: var(--p-overlay-popover-color);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

:deep(.vue-tel-input-custom .vti__dropdown-item) {
    color: var(--p-overlay-popover-color);
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--p-overlay-popover-border-color);
}

:deep(.vue-tel-input-custom .vti__dropdown-item:hover) {
    background-color: var(--p-overlay-popover-hover-background);
    color: var(--p-overlay-popover-hover-color);
}

:deep(.vue-tel-input-custom .vti__dropdown-item.highlighted) {
    background-color: var(--p-primary-color);
    color: var(--p-primary-contrast-color);
}

:deep(.vue-tel-input-custom .vti__search_box) {
    background-color: var(--p-inputtext-background);
    border: 1px solid var(--p-inputtext-border-color);
    border-radius: var(--p-inputtext-border-radius);
    color: var(--p-inputtext-color);
    margin: 8px;
    padding: 8px;
}

:deep(.vue-tel-input-custom .vti__search_box:focus) {
    border-color: var(--p-inputtext-focus-border-color);
    outline: none;
}

/* Dark mode specific adjustments */
.app-dark :deep(.vue-tel-input-custom .vti__dropdown) {
    background-color: var(--p-inputtext-background);
    border-color: var(--p-inputtext-border-color);
}

.app-dark :deep(.vue-tel-input-custom .vti__dropdown-list) {
    background-color: var(--p-overlay-popover-background);
    border-color: var(--p-overlay-popover-border-color);
}

.app-dark :deep(.vue-tel-input-custom .vti__dropdown-item) {
    color: var(--p-overlay-popover-color);
    border-bottom-color: var(--p-overlay-popover-border-color);
}

.app-dark :deep(.vue-tel-input-custom .vti__search_box) {
    background-color: var(--p-inputtext-background);
    border-color: var(--p-inputtext-border-color);
    color: var(--p-inputtext-color);
}

/* Error state styling */
:deep(.vue-tel-input-custom.error .vti__dropdown) {
    border-color: var(--p-inputtext-invalid-border-color);
}

/* Ensure proper sizing */
:deep(.vue-tel-input-custom .vti__dropdown) {
    min-height: 3rem;
    display: flex;
    align-items: center;
}
</style>
