{"name": "functions", "scripts": {"lint": "eslint", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@sendgrid/mail": "^8.1.4", "@types/twilio": "^3.19.2", "algoliasearch": "^4.25.2", "axios": "^1.8.4", "crypto-js": "^4.2.0", "express": "^4.21.2", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "googleapis": "^146.0.0", "openai": "^4.67.3", "parse-multipart-data": "^1.5.0", "twilio": "^5.5.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/multer": "^1.4.12", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^5.8.2"}, "private": true}